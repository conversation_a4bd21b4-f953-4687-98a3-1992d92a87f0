"""
DeepSeek量化交易系统 - ConfigManager单元测试

此模块包含ConfigManager类的完整单元测试，验证配置管理功能的正确性。
包括配置加载、验证、更新、环境变量覆盖、变更通知等功能的测试。

Author: DeepSeek Trading System
Date: 2025-07-31
"""

import unittest
import tempfile
import json
import os
import shutil
from pathlib import Path
from unittest.mock import patch, MagicMock
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from core.config_manager import ConfigManager, ConfigError, ConfigValidationRule


class TestConfigManager(unittest.TestCase):
    """ConfigManager单元测试类"""
    
    def setUp(self):
        """测试前置准备"""
        # 创建临时目录
        self.temp_dir = tempfile.mkdtemp()
        self.config_path = os.path.join(self.temp_dir, 'test_config.json')
        
        # 创建测试配置
        self.test_config = {
            "environment": "test",
            "api": {
                "okx": {
                    "api_key": "test_okx_key",
                    "secret_key": "test_okx_secret",
                    "passphrase": "test_okx_pass",
                    "sandbox": True,
                    "timeout": 10
                },
                "deepseek": {
                    "api_key": "test_deepseek_key",
                    "base_url": "https://api.deepseek.com",
                    "model": "deepseek-chat"
                }
            },
            "trading": {
                "max_leverage": 3.0,
                "max_position_ratio": 0.1,
                "confidence_threshold": 75.0,
                "stop_loss_pct": 0.02
            },
            "timeframes": ["1m", "5m", "15m"],
            "symbols": ["BTC-USDT", "ETH-USDT"],
            "server": {
                "host": "127.0.0.1",
                "port": 8000
            },
            "database": {
                "path": "test.db"
            },
            "logging": {
                "level": "DEBUG",
                "file": "test.log"
            }
        }
        
        # 写入测试配置文件
        with open(self.config_path, 'w', encoding='utf-8') as f:
            json.dump(self.test_config, f, indent=2)
    
    def tearDown(self):
        """测试后清理"""
        # 删除临时目录
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_init_with_existing_config(self):
        """测试使用现有配置文件初始化"""
        config = ConfigManager(self.config_path)
        
        # 验证配置加载正确
        self.assertEqual(config.get('environment'), 'test')
        self.assertEqual(config.get('api.okx.api_key'), 'test_okx_key')
        self.assertEqual(config.get('trading.max_leverage'), 3.0)
        self.assertEqual(config.get('timeframes'), ["1m", "5m", "15m"])
    
    def test_init_with_nonexistent_config(self):
        """测试使用不存在的配置文件初始化"""
        nonexistent_path = os.path.join(self.temp_dir, 'nonexistent.json')
        
        with patch('logging.getLogger'):
            config = ConfigManager(nonexistent_path)
            
            # 验证创建了默认配置
            self.assertEqual(config.get('environment'), 'development')
            self.assertTrue(os.path.exists(nonexistent_path))
    
    def test_init_with_invalid_json(self):
        """测试使用无效JSON文件初始化"""
        invalid_json_path = os.path.join(self.temp_dir, 'invalid.json')
        
        # 写入无效JSON
        with open(invalid_json_path, 'w') as f:
            f.write('{ invalid json }')
        
        # 验证抛出ConfigError
        with self.assertRaises(ConfigError):
            ConfigManager(invalid_json_path)
    
    def test_get_nested_key(self):
        """测试嵌套键访问"""
        config = ConfigManager(self.config_path)
        
        # 测试正常嵌套访问
        self.assertEqual(config.get('api.okx.api_key'), 'test_okx_key')
        self.assertEqual(config.get('trading.max_leverage'), 3.0)
        
        # 测试不存在的键
        self.assertIsNone(config.get('api.nonexistent.key'))
        self.assertEqual(config.get('api.nonexistent.key', 'default'), 'default')
        
        # 测试深层嵌套
        self.assertEqual(config.get('api.okx.timeout'), 10)
    
    def test_set_nested_key(self):
        """测试嵌套键设置"""
        config = ConfigManager(self.config_path)
        
        # 测试设置现有键
        config.set('trading.max_leverage', 5.0)
        self.assertEqual(config.get('trading.max_leverage'), 5.0)
        
        # 测试设置新键
        config.set('trading.new_param', 'new_value')
        self.assertEqual(config.get('trading.new_param'), 'new_value')
        
        # 测试创建新的嵌套结构
        config.set('new_section.new_key', 'test_value')
        self.assertEqual(config.get('new_section.new_key'), 'test_value')
    
    def test_update_batch(self):
        """测试批量更新配置"""
        config = ConfigManager(self.config_path)
        
        updates = {
            'trading.max_leverage': 4.0,
            'trading.stop_loss_pct': 0.03,
            'api.okx.timeout': 15,
            'new_config.test': 'value'
        }
        
        config.update(updates)
        
        # 验证所有更新都生效
        self.assertEqual(config.get('trading.max_leverage'), 4.0)
        self.assertEqual(config.get('trading.stop_loss_pct'), 0.03)
        self.assertEqual(config.get('api.okx.timeout'), 15)
        self.assertEqual(config.get('new_config.test'), 'value')
    
    def test_delete_config(self):
        """测试删除配置项"""
        config = ConfigManager(self.config_path)
        
        # 测试删除存在的键
        self.assertTrue(config.delete('trading.stop_loss_pct'))
        self.assertIsNone(config.get('trading.stop_loss_pct'))
        
        # 测试删除不存在的键
        self.assertFalse(config.delete('nonexistent.key'))
        
        # 测试删除嵌套键
        self.assertTrue(config.delete('api.okx.timeout'))
        self.assertIsNone(config.get('api.okx.timeout'))
    
    def test_change_listener(self):
        """测试配置变更监听器"""
        config = ConfigManager(self.config_path)
        
        # 设置监听器
        change_events = []
        
        def on_trading_change(key, new_val, old_val):
            change_events.append((key, new_val, old_val))
        
        def on_api_change(key, new_val, old_val):
            change_events.append(('api_changed', key, new_val, old_val))
        
        config.add_change_listener('trading', on_trading_change)
        config.add_change_listener('api', on_api_change)
        
        # 触发变更
        config.set('trading.max_leverage', 4.0)  # 旧值: 3.0
        config.set('api.okx.timeout', 20)        # 旧值: 10
        config.set('other.key', 'value')         # 不应该触发监听器
        
        # 验证监听器被正确调用
        self.assertEqual(len(change_events), 2)
        
        # 验证trading变更事件
        trading_event = [e for e in change_events if e[0] == 'trading.max_leverage'][0]
        self.assertEqual(trading_event, ('trading.max_leverage', 4.0, 3.0))
        
        # 验证api变更事件
        api_event = [e for e in change_events if e[0] == 'api_changed'][0]
        self.assertEqual(api_event[1], 'api.okx.timeout')  # key
        self.assertEqual(api_event[2], 20)                # new_val
        self.assertEqual(api_event[3], 10)                # old_val
    
    def test_remove_change_listener(self):
        """测试移除配置变更监听器"""
        config = ConfigManager(self.config_path)
        
        change_events = []
        
        def on_change(key, new_val, old_val):
            change_events.append((key, new_val, old_val))
        
        # 添加监听器
        config.add_change_listener('trading', on_change)
        
        # 触发变更
        config.set('trading.max_leverage', 4.0)
        self.assertEqual(len(change_events), 1)
        
        # 移除监听器
        config.remove_change_listener('trading', on_change)
        
        # 再次触发变更
        config.set('trading.max_leverage', 5.0)
        self.assertEqual(len(change_events), 1)  # 应该还是1，没有新增
    
    @patch.dict(os.environ, {
        'OKX_API_KEY': 'env_okx_key',
        'DEEPSEEK_API_KEY': 'env_deepseek_key',
        'SERVER_PORT': '9000',
        'OKX_SANDBOX': 'false',
        'DEBUG': 'true'
    })
    def test_env_overrides(self):
        """测试环境变量覆盖"""
        config = ConfigManager(self.config_path)
        
        # 验证环境变量覆盖了配置文件
        self.assertEqual(config.get('api.okx.api_key'), 'env_okx_key')
        self.assertEqual(config.get('api.deepseek.api_key'), 'env_deepseek_key')
        self.assertEqual(config.get('server.port'), 9000)  # 应该转换为int
        self.assertEqual(config.get('api.okx.sandbox'), False)  # 应该转换为bool
        self.assertEqual(config.get('server.reload'), True)  # DEBUG映射到server.reload
    
    def test_validation_success(self):
        """测试配置验证成功"""
        # 使用有效配置，不应该抛出异常
        try:
            config = ConfigManager(self.config_path)
            self.assertTrue(True)  # 如果到达这里说明验证通过
        except ConfigError:
            self.fail("有效配置验证失败")
    
    def test_validation_missing_required(self):
        """测试验证缺少必需配置"""
        # 创建缺少必需配置的配置文件
        invalid_config = {
            "environment": "test",
            "api": {
                "okx": {
                    # 缺少api_key
                    "secret_key": "test_secret",
                    "passphrase": "test_pass"
                }
            },
            "trading": {
                "max_leverage": 3.0,
                "max_position_ratio": 0.1,
                "confidence_threshold": 75.0
            },
            "timeframes": ["1m", "5m"],
            "symbols": ["BTC-USDT"]
        }
        
        invalid_path = os.path.join(self.temp_dir, 'invalid_config.json')
        with open(invalid_path, 'w', encoding='utf-8') as f:
            json.dump(invalid_config, f)
        
        # 验证抛出ConfigError
        with self.assertRaises(ConfigError) as cm:
            ConfigManager(invalid_path)
        
        self.assertIn('缺少必需配置项: api.okx.api_key', str(cm.exception))
    
    def test_validation_invalid_type(self):
        """测试验证无效类型"""
        # 创建类型错误的配置
        invalid_config = self.test_config.copy()
        invalid_config['trading']['max_leverage'] = "invalid_string"  # 应该是数字
        
        invalid_path = os.path.join(self.temp_dir, 'invalid_type_config.json')
        with open(invalid_path, 'w', encoding='utf-8') as f:
            json.dump(invalid_config, f)
        
        # 验证抛出ConfigError
        with self.assertRaises(ConfigError) as cm:
            ConfigManager(invalid_path)
        
        self.assertIn('类型错误', str(cm.exception))
    
    def test_validation_invalid_value(self):
        """测试验证无效值"""
        # 创建值超出范围的配置
        invalid_config = self.test_config.copy()
        invalid_config['trading']['max_leverage'] = 25.0  # 超出1.0-20.0范围
        
        invalid_path = os.path.join(self.temp_dir, 'invalid_value_config.json')
        with open(invalid_path, 'w', encoding='utf-8') as f:
            json.dump(invalid_config, f)
        
        # 验证抛出ConfigError
        with self.assertRaises(ConfigError) as cm:
            ConfigManager(invalid_value_config)
        
        self.assertIn('最大杠杆必须在1.0-20.0之间', str(cm.exception))
    
    def test_environment_detection(self):
        """测试环境检测方法"""
        config = ConfigManager(self.config_path)
        
        # 测试环境检测
        self.assertFalse(config.is_development())  # environment是test
        self.assertFalse(config.is_production())
        self.assertTrue(config.is_sandbox())  # okx.sandbox是true
        
        # 修改环境
        config.set('environment', 'development')
        self.assertTrue(config.is_development())
        self.assertFalse(config.is_production())
        
        config.set('environment', 'production')
        self.assertFalse(config.is_development())
        self.assertTrue(config.is_production())
    
    def test_get_all_config(self):
        """测试获取完整配置"""
        config = ConfigManager(self.config_path)
        
        all_config = config.get_all_config()
        
        # 验证返回完整配置
        self.assertEqual(all_config['environment'], 'test')
        self.assertEqual(all_config['api']['okx']['api_key'], 'test_okx_key')
        
        # 验证返回的是副本，修改不影响原配置
        all_config['environment'] = 'modified'
        self.assertEqual(config.get('environment'), 'test')
    
    def test_save_and_reload_config(self):
        """测试配置保存和重新加载"""
        config = ConfigManager(self.config_path)
        
        # 修改配置
        config.set('trading.max_leverage', 6.0)
        config.set('new_section.new_key', 'new_value')
        
        # 创建新的ConfigManager实例，验证配置已持久化
        config2 = ConfigManager(self.config_path)
        self.assertEqual(config2.get('trading.max_leverage'), 6.0)
        self.assertEqual(config2.get('new_section.new_key'), 'new_value')
    
    def test_thread_safety(self):
        """测试线程安全"""
        import threading
        import time
        
        config = ConfigManager(self.config_path)
        results = []
        errors = []
        
        def worker(worker_id):
            try:
                for i in range(10):
                    # 设置配置
                    config.set(f'worker_{worker_id}.value_{i}', i)
                    # 读取配置
                    value = config.get(f'worker_{worker_id}.value_{i}')
                    results.append((worker_id, i, value))
                    time.sleep(0.001)  # 短暂暂停以增加并发冲突可能性
            except Exception as e:
                errors.append((worker_id, str(e)))
        
        # 创建多个线程
        threads = []
        for i in range(5):
            thread = threading.Thread(target=worker, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 验证没有错误
        self.assertEqual(len(errors), 0, f"线程安全测试出现错误: {errors}")
        
        # 验证结果正确性
        self.assertEqual(len(results), 50)  # 5个线程 × 10次操作
        for worker_id, i, value in results:
            self.assertEqual(value, i, f"Worker {worker_id} 的值 {i} 不正确")
    
    def test_config_path_property(self):
        """测试配置文件路径属性"""
        config = ConfigManager(self.config_path)
        self.assertEqual(str(config.config_path), self.config_path)
    
    def test_string_representation(self):
        """测试字符串表示"""
        config = ConfigManager(self.config_path)
        
        str_repr = str(config)
        self.assertIn('ConfigManager', str_repr)
        self.assertIn(self.config_path, str_repr)
        self.assertIn('test', str_repr)  # environment
        
        # __repr__ 应该和 __str__ 相同
        self.assertEqual(str(config), repr(config))


class TestGlobalConfigFunctions(unittest.TestCase):
    """测试全局配置函数"""
    
    def setUp(self):
        """测试前置准备"""
        # 重置全局配置管理器
        import core.config_manager
        core.config_manager._config_manager = None
        
        # 创建临时配置文件
        self.temp_dir = tempfile.mkdtemp()
        self.config_path = os.path.join(self.temp_dir, 'global_test_config.json')
        
        test_config = {
            "environment": "test",
            "api": {
                "okx": {"api_key": "test", "secret_key": "test", "passphrase": "test"},
                "deepseek": {"api_key": "test"}
            },
            "trading": {"max_leverage": 3.0, "max_position_ratio": 0.1, "confidence_threshold": 75.0},
            "timeframes": ["1m", "5m"],
            "symbols": ["BTC-USDT"]
        }
        
        with open(self.config_path, 'w', encoding='utf-8') as f:
            json.dump(test_config, f)
    
    def tearDown(self):
        """测试后清理"""
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    @patch.dict(os.environ, {'ENVIRONMENT': 'production'})
    def test_get_config_with_env(self):
        """测试根据环境变量获取配置"""
        from core.config_manager import get_config
        
        # 创建production配置文件
        prod_config_path = os.path.join(self.temp_dir, 'config_production.json')
        with open(prod_config_path, 'w', encoding='utf-8') as f:
            json.dump({"environment": "production"}, f)
        
        # 切换到临时目录
        original_cwd = os.getcwd()
        try:
            os.chdir(self.temp_dir)
            
            config = get_config()
            # 由于验证会失败，这里主要测试路径选择逻辑
            # 实际项目中需要完整的production配置
            
        except ConfigError:
            # 预期的验证错误，忽略
            pass
        finally:
            os.chdir(original_cwd)
    
    def test_get_config_singleton(self):
        """测试单例模式"""
        from core.config_manager import get_config
        
        config1 = get_config(self.config_path)
        config2 = get_config()  # 第二次调用不传路径
        
        # 验证返回同一个实例
        self.assertIs(config1, config2)
    
    def test_reload_config(self):
        """测试重新加载配置"""
        from core.config_manager import get_config, reload_config
        
        config1 = get_config(self.config_path)
        original_id = id(config1)
        
        config2 = reload_config()
        new_id = id(config2)
        
        # 验证创建了新实例
        self.assertNotEqual(original_id, new_id)


if __name__ == '__main__':
    # 配置测试日志
    import logging
    logging.basicConfig(level=logging.WARNING)  # 减少测试过程中的日志输出
    
    # 运行测试
    unittest.main(verbosity=2)