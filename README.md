# DeepSeek加密货币全自动量化交易系统

基于DeepSeek AI模型的加密货币全自动量化交易系统，通过OKX交易所进行保证金交易。系统集成技术分析、AI决策、风险管理和自动交易执行。

## 🚀 核心功能

- **多时间周期技术分析**：集成TA-Lib，支持1分钟、5分钟、15分钟、1小时多时间周期分析
- **AI智能决策**：基于DeepSeek模型的智能交易决策，包含置信度分析
- **分离式交易引擎**：独立的开仓引擎和持仓引擎，专门的AI提示词策略
- **全面风险管理**：保证金交易风险控制，止盈止损，杠杆管理
- **Web管理界面**：基于Bootstrap 5.3.7的响应式管理界面
- **实时监控告警**：完整的系统监控和异常告警机制

## 🛠️ 技术栈

- **后端框架**: FastAPI (高性能异步)
- **AI模型**: DeepSeek API
- **交易所接口**: python-okx (OKX官方SDK)
- **技术分析**: TA-Lib
- **前端**: Jinja2 + Bootstrap 5.3.7
- **数据存储**: SQLite
- **任务调度**: APScheduler

## 📦 安装部署

### 1. 环境要求
- Python 3.8+
- Windows/Linux/macOS

### 2. 快速部署
```bash
# 1. 一键部署环境
python deploy.py

# 2. 配置API密钥
# 编辑 config_production.json 文件，填入OKX和DeepSeek API密钥

# 3. 启动系统
python main.py
```

### 3. 手动安装
```bash
# 安装依赖
pip install -r requirements.txt

# 初始化数据库
python scripts/init_database.py

# 配置系统
cp config_development.json config.json
# 编辑 config.json 填入API密钥

# 启动服务
python main.py
```

## 🎯 使用说明

1. **配置交易参数**：访问 http://localhost:8000 进入Web管理界面
2. **选择交易对**：在设置页面选择要交易的加密货币对
3. **设置风险参数**：配置最大杠杆、止盈止损比例等
4. **启动自动交易**：系统将自动执行数据分析和交易决策
5. **监控交易状态**：通过仪表板实时监控交易状态和盈亏

## ⚠️ 风险提示

- 加密货币交易存在高风险，可能导致本金损失
- 请在充分了解风险的前提下使用本系统
- 建议先在模拟盘环境中测试策略
- 请合理设置止损和仓位管理参数

## 📄 许可证

本项目仅供学习和研究使用。使用者需要承担所有交易风险。