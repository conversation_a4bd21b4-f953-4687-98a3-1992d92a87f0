"""
DeepSeek量化交易系统 - 日志系统

此模块提供完整的日志管理功能，支持结构化日志记录、多种输出格式、
动态日志级别调整、日志轮转和性能监控。

Features:
    - 结构化日志记录（structlog + 标准logging）
    - 多种输出格式（JSON、彩色控制台、文件）
    - 动态日志级别调整
    - 自动日志轮转和归档
    - 上下文信息追踪
    - 性能监控和统计
    - 敏感信息过滤

Author: DeepSeek Trading System
Date: 2025-07-31
"""

import logging
import logging.handlers
import structlog
import sys
import os
import json
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, TextIO
from dataclasses import dataclass, field
from datetime import datetime
import threading
from contextlib import contextmanager
import time
import traceback


@dataclass
class LogConfig:
    """日志配置数据类"""
    level: str = "INFO"
    file_path: Optional[str] = None
    max_file_size: str = "50MB"
    backup_count: int = 5
    format_type: str = "detailed"  # simple, detailed, json
    console_output: bool = True
    colored_output: bool = True
    structured_logging: bool = True
    sensitive_fields: List[str] = field(default_factory=lambda: [
        'password', 'secret', 'key', 'token', 'passphrase', 'api_key', 'secret_key'
    ])
    context_fields: List[str] = field(default_factory=lambda: [
        'user_id', 'session_id', 'request_id', 'trade_id', 'strategy_id'
    ])


class SensitiveDataFilter:
    """敏感数据过滤器"""
    
    def __init__(self, sensitive_fields: List[str]):
        """
        初始化敏感数据过滤器
        
        Args:
            sensitive_fields: 敏感字段列表
        """
        self.sensitive_fields = [field.lower() for field in sensitive_fields]
    
    def filter_dict(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        过滤字典中的敏感数据
        
        Args:
            data: 要过滤的字典
            
        Returns:
            过滤后的字典
        """
        if not isinstance(data, dict):
            return data
        
        filtered = {}
        for key, value in data.items():
            if key.lower() in self.sensitive_fields:
                filtered[key] = "***FILTERED***"
            elif isinstance(value, dict):
                filtered[key] = self.filter_dict(value)
            elif isinstance(value, list):
                filtered[key] = [
                    self.filter_dict(item) if isinstance(item, dict) else item
                    for item in value
                ]
            else:
                filtered[key] = value
        
        return filtered
    
    def filter_string(self, text: str) -> str:
        """
        过滤字符串中的敏感信息
        
        Args:
            text: 要过滤的字符串
            
        Returns:
            过滤后的字符串
        """
        import re
        
        # 过滤常见的敏感信息模式
        patterns = [
            (r'(api[_-]?key["\s]*[:=]["\s]*)([^"\s,}]+)', r'\1***FILTERED***'),
            (r'(secret[_-]?key["\s]*[:=]["\s]*)([^"\s,}]+)', r'\1***FILTERED***'),
            (r'(password["\s]*[:=]["\s]*)([^"\s,}]+)', r'\1***FILTERED***'),
            (r'(token["\s]*[:=]["\s]*)([^"\s,}]+)', r'\1***FILTERED***'),
        ]
        
        filtered_text = text
        for pattern, replacement in patterns:
            filtered_text = re.sub(pattern, replacement, filtered_text, flags=re.IGNORECASE)
        
        return filtered_text


class ContextManager:
    """日志上下文管理器"""
    
    def __init__(self):
        """初始化上下文管理器"""
        self._local = threading.local()
    
    def set_context(self, **kwargs) -> None:
        """
        设置当前线程的日志上下文
        
        Args:
            **kwargs: 上下文键值对
        """
        if not hasattr(self._local, 'context'):
            self._local.context = {}
        
        self._local.context.update(kwargs)
    
    def get_context(self) -> Dict[str, Any]:
        """
        获取当前线程的日志上下文
        
        Returns:
            上下文字典
        """
        if not hasattr(self._local, 'context'):
            return {}
        
        return self._local.context.copy()
    
    def clear_context(self) -> None:
        """清除当前线程的日志上下文"""
        if hasattr(self._local, 'context'):
            self._local.context.clear()
    
    @contextmanager
    def context(self, **kwargs):
        """
        上下文管理器，自动设置和清理上下文
        
        Args:
            **kwargs: 临时上下文键值对
        """
        original_context = self.get_context()
        
        try:
            self.set_context(**kwargs)
            yield
        finally:
            # 恢复原始上下文
            self._local.context = original_context


class PerformanceTracker:
    """性能追踪器"""
    
    def __init__(self):
        """初始化性能追踪器"""
        self._stats = {
            'log_counts': {},
            'avg_response_times': {},
            'error_counts': {},
            'start_time': time.time()
        }
        self._lock = threading.Lock()
    
    def track_log(self, level: str, response_time: float = None) -> None:
        """
        追踪日志统计
        
        Args:
            level: 日志级别
            response_time: 响应时间（可选）
        """
        with self._lock:
            # 统计日志数量
            if level not in self._stats['log_counts']:
                self._stats['log_counts'][level] = 0
            self._stats['log_counts'][level] += 1
            
            # 统计响应时间
            if response_time is not None:
                if level not in self._stats['avg_response_times']:
                    self._stats['avg_response_times'][level] = []
                
                times = self._stats['avg_response_times'][level]
                times.append(response_time)
                
                # 保持最近1000条记录
                if len(times) > 1000:
                    times.pop(0)
    
    def track_error(self, error_type: str) -> None:
        """
        追踪错误统计
        
        Args:
            error_type: 错误类型
        """
        with self._lock:
            if error_type not in self._stats['error_counts']:
                self._stats['error_counts'][error_type] = 0
            self._stats['error_counts'][error_type] += 1
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取性能统计信息
        
        Returns:
            统计信息字典
        """
        with self._lock:
            stats = self._stats.copy()
            
            # 计算平均响应时间
            avg_times = {}
            for level, times in stats['avg_response_times'].items():
                if times:
                    avg_times[level] = sum(times) / len(times)
            
            stats['avg_response_times'] = avg_times
            stats['uptime'] = time.time() - stats['start_time']
            
            return stats


class CustomFormatter(logging.Formatter):
    """自定义日志格式化器"""
    
    def __init__(self, config: LogConfig, sensitive_filter: SensitiveDataFilter):
        """
        初始化格式化器
        
        Args:
            config: 日志配置
            sensitive_filter: 敏感数据过滤器
        """
        self.config = config
        self.sensitive_filter = sensitive_filter
        
        # 设置格式
        if config.format_type == "simple":
            fmt = "%(levelname)s - %(message)s"
        elif config.format_type == "json":
            fmt = "%(message)s"  # JSON格式在format方法中处理
        else:  # detailed
            fmt = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        
        super().__init__(fmt, datefmt="%Y-%m-%d %H:%M:%S")
    
    def format(self, record: logging.LogRecord) -> str:
        """
        格式化日志记录
        
        Args:
            record: 日志记录
            
        Returns:
            格式化后的日志字符串
        """
        # 过滤敏感信息
        if hasattr(record, 'msg') and isinstance(record.msg, str):
            record.msg = self.sensitive_filter.filter_string(record.msg)
        
        if self.config.format_type == "json":
            return self._format_json(record)
        else:
            return super().format(record)
    
    def _format_json(self, record: logging.LogRecord) -> str:
        """
        格式化为JSON格式
        
        Args:
            record: 日志记录
            
        Returns:
            JSON格式的日志字符串
        """
        log_entry = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno
        }
        
        # 添加异常信息
        if record.exc_info:
            log_entry["exception"] = self.formatException(record.exc_info)
        
        # 添加自定义字段
        for key, value in record.__dict__.items():
            if key not in ['name', 'msg', 'args', 'levelname', 'levelno',
                          'pathname', 'filename', 'module', 'lineno',
                          'funcName', 'created', 'msecs', 'relativeCreated',
                          'thread', 'threadName', 'processName', 'process',
                          'getMessage', 'exc_info', 'exc_text', 'stack_info']:
                log_entry[key] = value
        
        # 过滤敏感数据
        log_entry = self.sensitive_filter.filter_dict(log_entry)
        
        return json.dumps(log_entry, ensure_ascii=False, default=str)


class ColoredConsoleHandler(logging.StreamHandler):
    """彩色控制台处理器"""
    
    # ANSI颜色代码
    COLORS = {
        'DEBUG': '\033[36m',     # 青色
        'INFO': '\033[32m',      # 绿色
        'WARNING': '\033[33m',   # 黄色
        'ERROR': '\033[31m',     # 红色
        'CRITICAL': '\033[35m',  # 紫色
        'RESET': '\033[0m'       # 重置
    }
    
    def __init__(self, stream: TextIO = None, use_colors: bool = True):
        """
        初始化彩色控制台处理器
        
        Args:
            stream: 输出流
            use_colors: 是否使用颜色
        """
        super().__init__(stream)
        self.use_colors = use_colors and hasattr(stream, 'isatty') and stream.isatty()
    
    def emit(self, record: logging.LogRecord) -> None:
        """
        输出日志记录
        
        Args:
            record: 日志记录
        """
        if self.use_colors:
            # 添加颜色
            color = self.COLORS.get(record.levelname, '')
            reset = self.COLORS['RESET']
            
            # 临时修改levelname以包含颜色
            original_levelname = record.levelname
            record.levelname = f"{color}{record.levelname}{reset}"
            
            try:
                super().emit(record)
            finally:
                # 恢复原始levelname
                record.levelname = original_levelname
        else:
            super().emit(record)


class LoggingSystem:
    """
    日志系统主类
    
    提供完整的日志管理功能，包括结构化日志记录、多种输出格式、
    动态配置、性能监控等。
    
    Features:
        - 结构化日志记录
        - 多种输出格式和处理器
        - 动态日志级别调整
        - 上下文信息管理
        - 性能监控和统计
        - 敏感信息过滤
        
    Example:
        >>> logging_system = LoggingSystem(config)
        >>> logger = logging_system.get_logger('trading')
        >>> logger.info("开始执行交易", symbol="BTC-USDT", action="buy")
    """
    
    def __init__(self, config: LogConfig):
        """
        初始化日志系统
        
        Args:
            config: 日志配置
        """
        self.config = config
        self.sensitive_filter = SensitiveDataFilter(config.sensitive_fields)
        self.context_manager = ContextManager()
        self.performance_tracker = PerformanceTracker()
        self._loggers: Dict[str, logging.Logger] = {}
        self._lock = threading.Lock()
        
        # 配置structlog
        if config.structured_logging:
            self._configure_structlog()
        
        # 设置根日志器
        self._configure_root_logger()
    
    def _configure_structlog(self) -> None:
        """配置structlog"""
        def add_context(logger, method_name, event_dict):
            """添加上下文信息"""
            context = self.context_manager.get_context()
            event_dict.update(context)
            return event_dict
        
        def add_timestamp(logger, method_name, event_dict):
            """添加时间戳"""
            event_dict['timestamp'] = datetime.now().isoformat()
            return event_dict
        
        def filter_sensitive(logger, method_name, event_dict):
            """过滤敏感信息"""
            return self.sensitive_filter.filter_dict(event_dict)
        
        # 配置structlog处理器
        processors = [
            add_context,
            add_timestamp,
            filter_sensitive,
            structlog.processors.add_log_level,
            structlog.processors.JSONRenderer() if self.config.format_type == "json" 
            else structlog.dev.ConsoleRenderer(colors=self.config.colored_output)
        ]
        
        structlog.configure(
            processors=processors,
            wrapper_class=structlog.make_filtering_bound_logger(
                getattr(logging, self.config.level.upper())
            ),
            logger_factory=structlog.stdlib.LoggerFactory(),
            cache_logger_on_first_use=True,
        )
    
    def _configure_root_logger(self) -> None:
        """配置根日志器"""
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, self.config.level.upper()))
        
        # 清除现有处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # 添加控制台处理器
        if self.config.console_output:
            console_handler = ColoredConsoleHandler(
                sys.stdout, 
                self.config.colored_output
            )
            console_handler.setFormatter(
                CustomFormatter(self.config, self.sensitive_filter)
            )
            root_logger.addHandler(console_handler)
        
        # 添加文件处理器
        if self.config.file_path:
            self._add_file_handler(root_logger)
    
    def _add_file_handler(self, logger: logging.Logger) -> None:
        """
        添加文件处理器
        
        Args:
            logger: 要添加处理器的日志器
        """
        # 确保日志目录存在
        log_path = Path(self.config.file_path)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 解析文件大小
        max_bytes = self._parse_file_size(self.config.max_file_size)
        
        # 创建轮转文件处理器
        file_handler = logging.handlers.RotatingFileHandler(
            filename=str(log_path),
            maxBytes=max_bytes,
            backupCount=self.config.backup_count,
            encoding='utf-8'
        )
        
        file_handler.setFormatter(
            CustomFormatter(self.config, self.sensitive_filter)
        )
        
        logger.addHandler(file_handler)
    
    def _parse_file_size(self, size_str: str) -> int:
        """
        解析文件大小字符串
        
        Args:
            size_str: 大小字符串，如 "50MB"
            
        Returns:
            字节数
        """
        size_str = size_str.upper().strip()
        
        multipliers = {
            'B': 1,
            'KB': 1024,
            'MB': 1024 * 1024,
            'GB': 1024 * 1024 * 1024
        }
        
        for suffix, multiplier in multipliers.items():
            if size_str.endswith(suffix):
                number = size_str[:-len(suffix)].strip()
                try:
                    return int(float(number) * multiplier)
                except ValueError:
                    break
        
        # 默认50MB
        return 50 * 1024 * 1024
    
    def get_logger(self, name: str) -> logging.Logger:
        """
        获取日志器
        
        Args:
            name: 日志器名称
            
        Returns:
            日志器实例
        """
        with self._lock:
            if name not in self._loggers:
                logger = logging.getLogger(name)
                
                # 设置日志级别
                logger.setLevel(getattr(logging, self.config.level.upper()))
                
                # 防止重复日志
                logger.propagate = True
                
                self._loggers[name] = logger
            
            return self._loggers[name]
    
    def get_structured_logger(self, name: str) -> Any:
        """
        获取结构化日志器
        
        Args:
            name: 日志器名称
            
        Returns:
            structlog日志器实例
        """
        if not self.config.structured_logging:
            raise RuntimeError("结构化日志未启用")
        
        return structlog.get_logger(name)
    
    def set_level(self, level: str) -> None:
        """
        动态设置日志级别
        
        Args:
            level: 日志级别
        """
        self.config.level = level.upper()
        log_level = getattr(logging, level.upper())
        
        # 更新所有日志器的级别
        logging.getLogger().setLevel(log_level)
        
        with self._lock:
            for logger in self._loggers.values():
                logger.setLevel(log_level)
    
    def add_context(self, **kwargs) -> None:
        """
        添加日志上下文
        
        Args:
            **kwargs: 上下文键值对
        """
        self.context_manager.set_context(**kwargs)
    
    def clear_context(self) -> None:
        """清除日志上下文"""
        self.context_manager.clear_context()
    
    @contextmanager
    def context(self, **kwargs):
        """
        临时上下文管理器
        
        Args:
            **kwargs: 临时上下文键值对
        """
        with self.context_manager.context(**kwargs):
            yield
    
    def log_performance(self, operation: str, duration: float, **kwargs) -> None:
        """
        记录性能信息
        
        Args:
            operation: 操作名称
            duration: 持续时间（秒）
            **kwargs: 额外信息
        """
        logger = self.get_logger('performance')
        
        # 追踪性能
        self.performance_tracker.track_log('INFO', duration)
        
        logger.info(
            f"性能监控 - {operation}",
            extra={
                'operation': operation,
                'duration': duration,
                'duration_ms': duration * 1000,
                **kwargs
            }
        )
    
    def log_error(self, error: Exception, context: Dict[str, Any] = None) -> None:
        """
        记录错误信息
        
        Args:
            error: 异常对象
            context: 错误上下文
        """
        logger = self.get_logger('error')
        
        # 追踪错误
        self.performance_tracker.track_error(type(error).__name__)
        
        error_info = {
            'error_type': type(error).__name__,
            'error_message': str(error),
            'traceback': traceback.format_exc()
        }
        
        if context:
            error_info.update(context)
        
        logger.error(
            f"系统错误 - {type(error).__name__}: {error}",
            extra=error_info,
            exc_info=True
        )
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取日志统计信息
        
        Returns:
            统计信息字典
        """
        return self.performance_tracker.get_stats()
    
    def shutdown(self) -> None:
        """关闭日志系统"""
        # 刷新所有处理器
        for handler in logging.getLogger().handlers:
            handler.flush()
            if hasattr(handler, 'close'):
                handler.close()
        
        # 关闭structlog
        if self.config.structured_logging:
            structlog.reset_defaults()


# 全局日志系统实例
_logging_system: Optional[LoggingSystem] = None


def get_logging_system(config: LogConfig = None) -> LoggingSystem:
    """
    获取全局日志系统实例
    
    Args:
        config: 日志配置，仅在首次调用时有效
        
    Returns:
        LoggingSystem实例
    """
    global _logging_system
    
    if _logging_system is None:
        if config is None:
            config = LogConfig()
        
        _logging_system = LoggingSystem(config)
    
    return _logging_system


def get_logger(name: str) -> logging.Logger:
    """
    获取日志器的便捷方法
    
    Args:
        name: 日志器名称
        
    Returns:
        日志器实例
    """
    logging_system = get_logging_system()
    return logging_system.get_logger(name)


def get_structured_logger(name: str) -> Any:
    """
    获取结构化日志器的便捷方法
    
    Args:
        name: 日志器名称
        
    Returns:
        structlog日志器实例
    """
    logging_system = get_logging_system()
    return logging_system.get_structured_logger(name)


@contextmanager
def log_context(**kwargs):
    """
    日志上下文管理器
    
    Args:
        **kwargs: 上下文键值对
    """
    logging_system = get_logging_system()
    with logging_system.context(**kwargs):
        yield


def log_performance(operation: str):
    """
    性能监控装饰器
    
    Args:
        operation: 操作名称
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                
                logging_system = get_logging_system()
                logging_system.log_performance(
                    operation, 
                    duration,
                    function=func.__name__,
                    args_count=len(args),
                    kwargs_count=len(kwargs)
                )
                
                return result
            except Exception as e:
                duration = time.time() - start_time
                logging_system = get_logging_system()
                logging_system.log_error(e, {
                    'operation': operation,
                    'function': func.__name__,
                    'duration': duration
                })
                raise
        
        return wrapper
    return decorator


if __name__ == "__main__":
    # 测试代码
    import tempfile
    
    # 创建测试配置
    temp_dir = tempfile.mkdtemp()
    log_file = os.path.join(temp_dir, 'test.log')
    
    config = LogConfig(
        level="DEBUG",
        file_path=log_file,
        format_type="detailed",
        console_output=True,
        colored_output=True,
        structured_logging=True
    )
    
    # 创建日志系统
    logging_system = LoggingSystem(config)
    
    print("=== 日志系统测试 ===")
    
    # 测试标准日志器
    logger = logging_system.get_logger('test')
    logger.debug("这是调试信息")
    logger.info("这是信息日志")
    logger.warning("这是警告日志")
    logger.error("这是错误日志")
    
    # 测试上下文
    with logging_system.context(user_id="12345", session_id="abcde"):
        logger.info("带上下文的日志")
    
    # 测试性能监控
    @log_performance("测试操作")
    def test_function():
        time.sleep(0.1)
        return "完成"
    
    result = test_function()
    print(f"函数结果: {result}")
    
    # 测试错误记录
    try:
        raise ValueError("测试错误")
    except ValueError as e:
        logging_system.log_error(e, {"test_context": "错误测试"})
    
    # 测试结构化日志器
    if config.structured_logging:
        struct_logger = logging_system.get_structured_logger('structured_test')
        struct_logger.info("结构化日志测试", key="value", number=42)
    
    # 显示统计信息
    stats = logging_system.get_stats()
    print(f"\n=== 日志统计 ===")
    print(f"日志数量: {stats['log_counts']}")
    print(f"错误数量: {stats['error_counts']}")
    print(f"运行时间: {stats['uptime']:.2f}秒")
    
    # 清理
    logging_system.shutdown()
    
    print("\n日志系统测试完成")
    print(f"日志文件: {log_file}")