"""
DeepSeek量化交易系统 - 配置管理器

此模块提供配置文件的加载、验证、动态更新和环境变量覆盖功能。
支持开发和生产环境的配置管理，确保配置的安全性和灵活性。

Author: DeepSeek Trading System
Date: 2025-07-31
"""

import json
import os
import logging
from pathlib import Path
from typing import Any, Dict, List, Optional, Callable
from dataclasses import dataclass
from threading import Lock


@dataclass
class ConfigValidationRule:
    """配置验证规则"""
    key: str
    required: bool = True
    data_type: type = str
    validator: Optional[Callable[[Any], bool]] = None
    error_message: str = ""


class ConfigError(Exception):
    """配置相关异常"""
    pass


class ConfigManager:
    """
    配置管理器
    
    提供配置文件的加载、验证、动态更新和环境变量覆盖功能。
    支持配置变更通知机制，允许其他组件响应配置变化。
    
    Features:
        - JSON配置文件加载和保存
        - 环境变量覆盖配置文件设置
        - 配置验证和类型检查
        - 动态配置更新
        - 配置变更通知机制
        - 嵌套配置键访问（支持点号分割）
        
    Example:
        >>> config = ConfigManager('config_production.json')
        >>> api_key = config.get('api.okx.api_key')
        >>> config.set('trading.max_leverage', 5.0)
        >>> config.add_change_listener('trading', on_trading_config_change)
    """
    
    def __init__(self, config_path: str = 'config_production.json'):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件路径
            
        Raises:
            ConfigError: 配置文件加载或验证失败时抛出
        """
        self.config_path = Path(config_path)
        self.config: Dict[str, Any] = {}
        self.change_listeners: Dict[str, List[Callable]] = {}
        self._lock = Lock()
        self._logger = logging.getLogger(__name__)
        
        # 配置验证规则
        self.validation_rules = self._get_validation_rules()
        
        # 加载配置
        self.load_config()
        
        # 验证配置
        self._validate_config()
    
    def _get_validation_rules(self) -> List[ConfigValidationRule]:
        """
        获取配置验证规则
        
        Returns:
            配置验证规则列表
        """
        return [
            # API配置验证
            ConfigValidationRule(
                key='api.okx.api_key',
                required=True,
                data_type=str,
                validator=lambda x: len(x.strip()) > 0,
                error_message='OKX API Key不能为空'
            ),
            ConfigValidationRule(
                key='api.okx.secret_key',
                required=True,
                data_type=str,
                validator=lambda x: len(x.strip()) > 0,
                error_message='OKX Secret Key不能为空'
            ),
            ConfigValidationRule(
                key='api.okx.passphrase',
                required=True,
                data_type=str,
                validator=lambda x: len(x.strip()) > 0,
                error_message='OKX Passphrase不能为空'
            ),
            ConfigValidationRule(
                key='api.deepseek.api_key',
                required=True,
                data_type=str,
                validator=lambda x: len(x.strip()) > 0,
                error_message='DeepSeek API Key不能为空'
            ),
            
            # 交易配置验证
            ConfigValidationRule(
                key='trading.max_leverage',
                required=True,
                data_type=(int, float),
                validator=lambda x: 1.0 <= x <= 20.0,
                error_message='最大杠杆必须在1.0-20.0之间'
            ),
            ConfigValidationRule(
                key='trading.max_position_ratio',
                required=True,
                data_type=(int, float),
                validator=lambda x: 0.01 <= x <= 1.0,
                error_message='最大仓位比例必须在0.01-1.0之间'
            ),
            ConfigValidationRule(
                key='trading.confidence_threshold',
                required=True,
                data_type=(int, float),
                validator=lambda x: 0.0 <= x <= 100.0,
                error_message='置信度阈值必须在0.0-100.0之间'
            ),
            
            # 时间周期验证
            ConfigValidationRule(
                key='timeframes',
                required=True,
                data_type=list,
                validator=lambda x: len(x) > 0 and all(
                    tf in ['1m', '3m', '5m', '15m', '30m', '1h', '2h', '4h', '6h', '8h', '12h', '1d'] 
                    for tf in x
                ),
                error_message='时间周期格式不正确'
            ),
            
            # 交易对验证
            ConfigValidationRule(
                key='symbols',
                required=True,
                data_type=list,
                validator=lambda x: len(x) > 0 and all(
                    isinstance(symbol, str) and '-' in symbol for symbol in x
                ),
                error_message='交易对格式不正确，应为类似BTC-USDT的格式'
            ),
        ]
    
    def load_config(self) -> None:
        """
        加载配置文件
        
        首先尝试加载指定的配置文件，如果文件不存在则创建默认配置。
        然后应用环境变量覆盖。
        
        Raises:
            ConfigError: 配置文件格式错误时抛出
        """
        try:
            if not self.config_path.exists():
                self._logger.warning(f"配置文件 {self.config_path} 不存在，创建默认配置")
                self.config = self._get_default_config()
                self.save_config()
            else:
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
                self._logger.info(f"成功加载配置文件: {self.config_path}")
            
            # 应用环境变量覆盖
            self._apply_env_overrides()
            
        except json.JSONDecodeError as e:
            raise ConfigError(f"配置文件格式错误: {e}")
        except Exception as e:
            raise ConfigError(f"加载配置文件失败: {e}")
    
    def save_config(self) -> None:
        """
        保存配置文件
        
        将当前配置保存到文件，确保目录存在。
        
        Raises:
            ConfigError: 保存失败时抛出
        """
        try:
            # 确保目录存在
            self.config_path.parent.mkdir(parents=True, exist_ok=True)
            
            with self._lock:
                with open(self.config_path, 'w', encoding='utf-8') as f:
                    json.dump(self.config, f, indent=2, ensure_ascii=False)
            
            self._logger.info(f"配置文件已保存: {self.config_path}")
            
        except Exception as e:
            raise ConfigError(f"保存配置文件失败: {e}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        支持点号分割的嵌套键访问，如 'api.okx.api_key'
        
        Args:
            key: 配置键，支持嵌套访问
            default: 默认值，当键不存在时返回
            
        Returns:
            配置值或默认值
            
        Example:
            >>> config.get('api.okx.api_key')
            >>> config.get('trading.max_leverage', 5.0)
        """
        keys = key.split('.')
        value = self.config
        
        try:
            for k in keys:
                if isinstance(value, dict) and k in value:
                    value = value[k]
                else:
                    return default
            return value
        except Exception:
            return default
    
    def set(self, key: str, value: Any) -> None:
        """
        设置配置值
        
        支持点号分割的嵌套键设置，自动创建嵌套字典结构。
        设置完成后会触发配置变更通知。
        
        Args:
            key: 配置键，支持嵌套设置
            value: 配置值
            
        Example:
            >>> config.set('trading.max_leverage', 5.0)
            >>> config.set('api.okx.timeout', 30)
        """
        keys = key.split('.')
        config = self.config
        
        with self._lock:
            # 创建嵌套结构
            for k in keys[:-1]:
                if k not in config:
                    config[k] = {}
                elif not isinstance(config[k], dict):
                    config[k] = {}
                config = config[k]
            
            # 设置值
            old_value = config.get(keys[-1])
            config[keys[-1]] = value
            
            # 保存配置
            self.save_config()
            
            # 触发变更通知
            if old_value != value:
                self._notify_config_change(key, value, old_value)
    
    def update(self, updates: Dict[str, Any]) -> None:
        """
        批量更新配置
        
        Args:
            updates: 要更新的配置字典
            
        Example:
            >>> config.update({
            ...     'trading.max_leverage': 5.0,
            ...     'trading.stop_loss_pct': 0.02
            ... })
        """
        for key, value in updates.items():
            self.set(key, value)
    
    def delete(self, key: str) -> bool:
        """
        删除配置项
        
        Args:
            key: 要删除的配置键
            
        Returns:
            是否成功删除
        """
        keys = key.split('.')
        config = self.config
        
        try:
            with self._lock:
                # 找到父级配置
                for k in keys[:-1]:
                    if isinstance(config, dict) and k in config:
                        config = config[k]
                    else:
                        return False  # 键不存在
                
                # 删除最后一级键
                if isinstance(config, dict) and keys[-1] in config:
                    old_value = config[keys[-1]]
                    del config[keys[-1]]
                    
                    # 保存配置
                    self.save_config()
                    
                    # 触发变更通知
                    self._notify_config_change(key, None, old_value)
                    return True
                
                return False
                
        except Exception as e:
            self._logger.error(f"删除配置项失败: {e}")
            return False
    
    def add_change_listener(self, key_pattern: str, callback: Callable[[str, Any, Any], None]) -> None:
        """
        添加配置变更监听器
        
        Args:
            key_pattern: 监听的配置键模式，支持前缀匹配
            callback: 回调函数，参数为(key, new_value, old_value)
            
        Example:
            >>> def on_trading_change(key, new_val, old_val):
            ...     print(f"交易配置变更: {key} = {new_val}")
            >>> config.add_change_listener('trading', on_trading_change)
        """
        if key_pattern not in self.change_listeners:
            self.change_listeners[key_pattern] = []
        
        self.change_listeners[key_pattern].append(callback)
        self._logger.debug(f"已添加配置变更监听器: {key_pattern}")
    
    def remove_change_listener(self, key_pattern: str, callback: Callable) -> None:
        """
        移除配置变更监听器
        
        Args:
            key_pattern: 配置键模式
            callback: 要移除的回调函数
        """
        if key_pattern in self.change_listeners:
            try:
                self.change_listeners[key_pattern].remove(callback)
                if not self.change_listeners[key_pattern]:
                    del self.change_listeners[key_pattern]
                self._logger.debug(f"已移除配置变更监听器: {key_pattern}")
            except ValueError:
                pass
    
    def _notify_config_change(self, key: str, new_value: Any, old_value: Any) -> None:
        """
        通知配置变更
        
        Args:
            key: 变更的配置键
            new_value: 新值
            old_value: 旧值
        """
        for pattern, callbacks in self.change_listeners.items():
            if key.startswith(pattern):
                for callback in callbacks:
                    try:
                        callback(key, new_value, old_value)
                    except Exception as e:
                        self._logger.error(f"配置变更回调执行失败: {e}")
    
    def _apply_env_overrides(self) -> None:
        """
        应用环境变量覆盖
        
        环境变量命名规则：
        - 使用下划线分割嵌套键
        - 全部大写
        - 例如：API_OKX_API_KEY 对应 api.okx.api_key
        """
        env_mappings = {
            'ENVIRONMENT': 'environment',
            'OKX_API_KEY': 'api.okx.api_key',
            'OKX_SECRET_KEY': 'api.okx.secret_key',
            'OKX_PASSPHRASE': 'api.okx.passphrase',
            'OKX_SANDBOX': 'api.okx.sandbox',
            'DEEPSEEK_API_KEY': 'api.deepseek.api_key',
            'DEEPSEEK_BASE_URL': 'api.deepseek.base_url',
            'DATABASE_PATH': 'database.path',
            'REDIS_URL': 'cache.redis_url',
            'LOG_LEVEL': 'logging.level',
            'LOG_FILE': 'logging.file',
            'SERVER_HOST': 'server.host',
            'SERVER_PORT': 'server.port',
            'DEBUG': 'server.reload',
        }
        
        for env_key, config_key in env_mappings.items():
            env_value = os.getenv(env_key)
            if env_value is not None:
                # 类型转换
                if env_key in ['SERVER_PORT']:
                    env_value = int(env_value)
                elif env_key in ['OKX_SANDBOX', 'DEBUG']:
                    env_value = env_value.lower() in ('true', '1', 'yes', 'on')
                
                # 设置配置值（不触发保存和通知）
                keys = config_key.split('.')
                config = self.config
                for k in keys[:-1]:
                    if k not in config:
                        config[k] = {}
                    config = config[k]
                config[keys[-1]] = env_value
                
                self._logger.debug(f"环境变量覆盖: {config_key} = {env_value}")
    
    def _validate_config(self) -> None:
        """
        验证配置
        
        根据验证规则检查配置的完整性和正确性。
        
        Raises:
            ConfigError: 配置验证失败时抛出
        """
        errors = []
        
        for rule in self.validation_rules:
            value = self.get(rule.key)
            
            # 检查必需项
            if rule.required and value is None:
                errors.append(f"缺少必需配置项: {rule.key}")
                continue
            
            # 跳过可选的空值
            if not rule.required and value is None:
                continue
            
            # 检查数据类型
            if not isinstance(value, rule.data_type):
                errors.append(f"配置项 {rule.key} 类型错误，期望 {rule.data_type}，实际 {type(value)}")
                continue
            
            # 检查自定义验证器
            if rule.validator and not rule.validator(value):
                error_msg = rule.error_message or f"配置项 {rule.key} 验证失败"
                errors.append(error_msg)
        
        if errors:
            raise ConfigError(f"配置验证失败:\n" + "\n".join(f"- {error}" for error in errors))
        
        self._logger.info("配置验证通过")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """
        获取默认配置
        
        Returns:
            默认配置字典
        """
        return {
            "environment": "development",
            "trading": {
                "max_leverage": 5.0,
                "max_position_ratio": 0.1,
                "stop_loss_pct": 0.02,
                "take_profit_pct": 0.06,
                "confidence_threshold": 70.0,
                "min_trade_amount": 10.0,
                "max_daily_trades": 100
            },
            "api": {
                "okx": {
                    "api_key": "",
                    "secret_key": "",
                    "passphrase": "",
                    "sandbox": True,
                    "timeout": 10
                },
                "deepseek": {
                    "api_key": "",
                    "base_url": "https://api.deepseek.com",
                    "model": "deepseek-chat",
                    "timeout": 30,
                    "max_tokens": 1000
                }
            },
            "timeframes": ["1m", "5m", "15m", "1h"],
            "symbols": ["BTC-USDT", "ETH-USDT"],
            "server": {
                "host": "127.0.0.1",
                "port": 8000,
                "workers": 1,
                "reload": True
            },
            "database": {
                "path": "data/trading_system.db",
                "connection_pool_size": 5,
                "query_timeout": 30
            },
            "logging": {
                "level": "INFO",
                "file": "logs/trading_system.log",
                "max_file_size": "50MB",
                "backup_count": 5,
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            },
            "cache": {
                "redis_url": "redis://localhost:6379/0",
                "ttl": {
                    "market_data": 60,
                    "indicators": 300,
                    "account_info": 30,
                    "positions": 15
                }
            },
            "monitoring": {
                "enabled": True,
                "metrics_interval": 60,
                "health_check_interval": 300,
                "alert_email": "",
                "alert_webhook": ""
            },
            "scheduler": {
                "timezone": "Asia/Shanghai",
                "data_collection_interval": 60,
                "trading_execution_interval": 300,
                "risk_monitoring_interval": 30,
                "cleanup_interval": 3600
            },
            "security": {
                "encryption_key_path": "keys/encryption.key",
                "session_timeout": 3600,
                "max_login_attempts": 5,
                "ip_whitelist": []
            }
        }
    
    def get_all_config(self) -> Dict[str, Any]:
        """
        获取完整配置
        
        Returns:
            配置字典的副本
        """
        return self.config.copy()
    
    def is_development(self) -> bool:
        """
        判断是否为开发环境
        
        Returns:
            是否为开发环境
        """
        return self.get('environment', 'production') == 'development'
    
    def is_production(self) -> bool:
        """
        判断是否为生产环境
        
        Returns:
            是否为生产环境
        """
        return self.get('environment', 'production') == 'production'
    
    def is_sandbox(self) -> bool:
        """
        判断是否为沙盒环境
        
        Returns:
            是否启用沙盒模式
        """
        return self.get('api.okx.sandbox', True)
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"ConfigManager(config_path={self.config_path}, environment={self.get('environment')})"
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        return self.__str__()


# 全局配置管理器实例（延迟初始化）
_config_manager: Optional[ConfigManager] = None


def get_config(config_path: str = None) -> ConfigManager:
    """
    获取全局配置管理器实例
    
    采用单例模式，确保全局只有一个配置管理器实例。
    
    Args:
        config_path: 配置文件路径，仅在首次调用时有效
        
    Returns:
        ConfigManager实例
    """
    global _config_manager
    
    if _config_manager is None:
        if config_path is None:
            # 根据环境变量决定使用哪个配置文件
            env = os.getenv('ENVIRONMENT', 'production')
            config_path = f'config_{env}.json'
        
        _config_manager = ConfigManager(config_path)
    
    return _config_manager


def reload_config() -> ConfigManager:
    """
    重新加载配置
    
    强制重新创建配置管理器实例。
    
    Returns:
        新的ConfigManager实例
    """
    global _config_manager
    _config_manager = None
    return get_config()


if __name__ == "__main__":
    # 测试代码
    import tempfile
    import os
    
    # 创建临时配置文件进行测试
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        test_config = {
            "environment": "test",
            "api": {
                "okx": {
                    "api_key": "test_key",
                    "secret_key": "test_secret",
                    "passphrase": "test_pass"
                },
                "deepseek": {
                    "api_key": "test_deepseek_key"
                }
            },
            "trading": {
                "max_leverage": 3.0
            },
            "timeframes": ["1m", "5m"],
            "symbols": ["BTC-USDT"]
        }
        json.dump(test_config, f)
        temp_path = f.name
    
    try:
        # 测试配置管理器
        config = ConfigManager(temp_path)
        
        print("=== 配置管理器测试 ===")
        print(f"API Key: {config.get('api.okx.api_key')}")
        print(f"最大杠杆: {config.get('trading.max_leverage')}")
        print(f"是否开发环境: {config.is_development()}")
        
        # 测试配置设置
        config.set('trading.max_leverage', 5.0)
        print(f"更新后最大杠杆: {config.get('trading.max_leverage')}")
        
        # 测试变更监听
        def on_change(key, new_val, old_val):
            print(f"配置变更: {key} {old_val} -> {new_val}")
        
        config.add_change_listener('trading', on_change)
        config.set('trading.stop_loss_pct', 0.03)
        
        print("配置管理器测试完成")
        
    finally:
        # 清理临时文件
        os.unlink(temp_path)