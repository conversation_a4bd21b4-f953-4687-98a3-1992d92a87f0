"""
DeepSeek量化交易系统 - 基础数据模型

此模块定义了所有数据模型的基础类和通用Mixin，
提供标准化的数据验证、序列化和时间戳功能。

Author: DeepSeek Trading System
Date: 2025-07-31
"""

from datetime import datetime, timezone
from typing import Any, Dict, Optional, Union, List, ClassVar
from decimal import Decimal
import json
from pydantic import BaseModel as PydanticBaseModel, Field, validator, root_validator
from pydantic.config import ConfigDict


class BaseModel(PydanticBaseModel):
    """
    基础数据模型
    
    为所有数据模型提供统一的基础功能，包括:
    - 严格的数据验证
    - JSON序列化/反序列化
    - 字段别名支持
    - 自定义验证器
    """
    
    model_config = ConfigDict(
        # 严格模式，禁止额外字段
        extra='forbid',
        # 使用枚举值而不是名称
        use_enum_values=True,
        # 验证赋值
        validate_assignment=True,
        # 允许字段别名
        populate_by_name=True,
        # JSON编码器配置
        json_encoders={
            datetime: lambda v: v.isoformat(),
            Decimal: lambda v: str(v)
        }
    )
    
    def to_dict(self, exclude_none: bool = True) -> Dict[str, Any]:
        """
        转换为字典
        
        Args:
            exclude_none: 是否排除None值
            
        Returns:
            字典表示
        """
        return self.model_dump(exclude_none=exclude_none)
    
    def to_json(self, exclude_none: bool = True, ensure_ascii: bool = False) -> str:
        """
        转换为JSON字符串
        
        Args:
            exclude_none: 是否排除None值
            ensure_ascii: 是否确保ASCII编码
            
        Returns:
            JSON字符串
        """
        return self.model_dump_json(exclude_none=exclude_none)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'BaseModel':
        """
        从字典创建实例
        
        Args:
            data: 数据字典
            
        Returns:
            模型实例
        """
        return cls(**data)
    
    @classmethod
    def from_json(cls, json_str: str) -> 'BaseModel':
        """
        从JSON字符串创建实例
        
        Args:
            json_str: JSON字符串
            
        Returns:
            模型实例
        """
        return cls.model_validate_json(json_str)
    
    def update(self, **kwargs) -> 'BaseModel':
        """
        更新字段并返回新实例
        
        Args:
            **kwargs: 要更新的字段
            
        Returns:
            更新后的新实例
        """
        data = self.to_dict()
        data.update(kwargs)
        return self.__class__(**data)
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"{self.__class__.__name__}({self.to_json()})"
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        return self.__str__()


class TimestampMixin(BaseModel):
    """
    时间戳Mixin
    
    为数据模型提供标准的时间戳字段和相关功能。
    """
    
    timestamp: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="时间戳（UTC）"
    )
    
    created_at: Optional[datetime] = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="创建时间（UTC）"
    )
    
    updated_at: Optional[datetime] = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="更新时间（UTC）"
    )
    
    @validator('timestamp', 'created_at', 'updated_at', pre=True)
    def ensure_utc_timezone(cls, v):
        """确保时间戳使用UTC时区"""
        if v is None:
            return v
        
        if isinstance(v, str):
            # 解析ISO格式时间字符串
            try:
                v = datetime.fromisoformat(v.replace('Z', '+00:00'))
            except ValueError:
                # 尝试其他格式
                v = datetime.strptime(v, '%Y-%m-%d %H:%M:%S')
        
        if isinstance(v, datetime):
            if v.tzinfo is None:
                # 如果没有时区信息，假设为UTC
                v = v.replace(tzinfo=timezone.utc)
            else:
                # 转换为UTC
                v = v.astimezone(timezone.utc)
        
        return v
    
    def is_stale(self, max_age_seconds: int = 60) -> bool:
        """
        判断数据是否过期
        
        Args:
            max_age_seconds: 最大年龄（秒）
            
        Returns:
            是否过期
        """
        now = datetime.now(timezone.utc)
        age = (now - self.timestamp).total_seconds()
        return age > max_age_seconds
    
    def age_seconds(self) -> float:
        """
        获取数据年龄（秒）
        
        Returns:
            数据年龄
        """
        now = datetime.now(timezone.utc)
        return (now - self.timestamp).total_seconds()


class ValidationMixin(BaseModel):
    """
    验证Mixin
    
    提供通用的数据验证功能。
    """
    
    @validator('*', pre=True)
    def strip_strings(cls, v):
        """去除字符串前后空格"""
        if isinstance(v, str):
            return v.strip()
        return v
    
    @root_validator(pre=True)
    def convert_decimal_strings(cls, values):
        """将数字字符串转换为适当的类型"""
        for key, value in values.items():
            if isinstance(value, str) and value.replace('.', '').replace('-', '').isdigit():
                # 如果字段注解是Decimal，转换为Decimal
                field_info = cls.__fields__.get(key)
                if field_info and field_info.annotation == Decimal:
                    values[key] = Decimal(value)
                # 如果包含小数点，转换为float
                elif '.' in value:
                    try:
                        values[key] = float(value)
                    except ValueError:
                        pass
                # 否则尝试转换为int
                else:
                    try:
                        values[key] = int(value)
                    except ValueError:
                        pass
        
        return values
    
    def validate_positive(self, value: Union[int, float, Decimal], field_name: str = '') -> Union[int, float, Decimal]:
        """
        验证正数
        
        Args:
            value: 要验证的值
            field_name: 字段名称
            
        Returns:
            验证后的值
            
        Raises:
            ValueError: 如果值不是正数
        """
        if value <= 0:
            raise ValueError(f'{field_name or "Value"} must be positive, got {value}')
        return value
    
    def validate_percentage(self, value: Union[int, float, Decimal], field_name: str = '') -> Union[int, float, Decimal]:
        """
        验证百分比（0-100）
        
        Args:
            value: 要验证的值
            field_name: 字段名称
            
        Returns:
            验证后的值
            
        Raises:
            ValueError: 如果值不在0-100范围内
        """
        if not (0 <= value <= 100):
            raise ValueError(f'{field_name or "Percentage"} must be between 0 and 100, got {value}')
        return value
    
    def validate_ratio(self, value: Union[int, float, Decimal], field_name: str = '') -> Union[int, float, Decimal]:
        """
        验证比率（0-1）
        
        Args:
            value: 要验证的值
            field_name: 字段名称
            
        Returns:
            验证后的值
            
        Raises:
            ValueError: 如果值不在0-1范围内
        """
        if not (0 <= value <= 1):
            raise ValueError(f'{field_name or "Ratio"} must be between 0 and 1, got {value}')
        return value


class PaginationModel(BaseModel):
    """
    分页模型
    
    提供标准的分页信息。
    """
    
    page: int = Field(ge=1, description="页码（从1开始）")
    page_size: int = Field(ge=1, le=1000, description="每页大小")
    total: int = Field(ge=0, description="总记录数")
    total_pages: int = Field(ge=0, description="总页数")
    has_next: bool = Field(description="是否有下一页")
    has_prev: bool = Field(description="是否有上一页")
    
    @root_validator
    def calculate_pagination_info(cls, values):
        """计算分页信息"""
        page = values.get('page', 1)
        page_size = values.get('page_size', 10)
        total = values.get('total', 0)
        
        total_pages = (total + page_size - 1) // page_size if total > 0 else 0
        has_next = page < total_pages
        has_prev = page > 1
        
        values.update({
            'total_pages': total_pages,
            'has_next': has_next,
            'has_prev': has_prev
        })
        
        return values


class ResponseModel(BaseModel):
    """
    API响应模型
    
    提供标准的API响应格式。
    """
    
    success: bool = Field(description="是否成功")
    message: str = Field(description="响应消息")
    data: Optional[Any] = Field(None, description="响应数据")
    error_code: Optional[str] = Field(None, description="错误代码")
    timestamp: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="响应时间戳"
    )
    
    @classmethod
    def success_response(cls, data: Any = None, message: str = "操作成功") -> 'ResponseModel':
        """
        创建成功响应
        
        Args:
            data: 响应数据
            message: 响应消息
            
        Returns:
            成功响应
        """
        return cls(
            success=True,
            message=message,
            data=data
        )
    
    @classmethod
    def error_response(cls, message: str, error_code: str = None, data: Any = None) -> 'ResponseModel':
        """
        创建错误响应
        
        Args:
            message: 错误消息
            error_code: 错误代码
            data: 响应数据
            
        Returns:
            错误响应
        """
        return cls(
            success=False,
            message=message,
            error_code=error_code,
            data=data
        )


class MetadataModel(BaseModel):
    """
    元数据模型
    
    提供通用的元数据信息。
    """
    
    source: str = Field(description="数据来源")
    version: str = Field(default="1.0", description="数据版本")
    checksum: Optional[str] = Field(None, description="数据校验和")
    tags: List[str] = Field(default_factory=list, description="标签")
    extra: Dict[str, Any] = Field(default_factory=dict, description="额外信息")
    
    def add_tag(self, tag: str) -> None:
        """
        添加标签
        
        Args:
            tag: 标签名称
        """
        if tag not in self.tags:
            self.tags.append(tag)
    
    def remove_tag(self, tag: str) -> None:
        """
        移除标签
        
        Args:
            tag: 标签名称
        """
        if tag in self.tags:
            self.tags.remove(tag)
    
    def has_tag(self, tag: str) -> bool:
        """
        检查是否有指定标签
        
        Args:
            tag: 标签名称
            
        Returns:
            是否存在标签
        """
        return tag in self.tags