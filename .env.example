# DeepSeek量化交易系统环境变量配置示例
# 复制此文件为 .env 并填入实际值

# 环境设置
ENVIRONMENT=development

# OKX交易所API配置
OKX_API_KEY=your_okx_api_key_here
OKX_SECRET_KEY=your_okx_secret_key_here
OKX_PASSPHRASE=your_okx_passphrase_here
OKX_SANDBOX=true

# DeepSeek AI API配置  
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_BASE_URL=https://api.deepseek.com

# 数据库配置
DATABASE_PATH=data/trading_system.db

# Redis缓存配置
REDIS_URL=redis://localhost:6379/0

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/trading_system.log

# 服务器配置
SERVER_HOST=127.0.0.1
SERVER_PORT=8000

# 安全配置
ENCRYPTION_KEY_PATH=keys/encryption.key
SESSION_SECRET=your_session_secret_here

# 监控配置
ALERT_EMAIL=<EMAIL>
ALERT_WEBHOOK=https://your-webhook-url.com

# 调试模式
DEBUG=false