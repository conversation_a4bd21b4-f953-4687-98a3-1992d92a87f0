"""
DeepSeek量化交易系统 - LoggingSystem单元测试

此模块包含LoggingSystem类的完整单元测试，验证日志系统功能的正确性。
包括日志记录、格式化、过滤、上下文管理、性能监控等功能的测试。

Author: DeepSeek Trading System
Date: 2025-07-31
"""

import unittest
import tempfile
import os
import json
import shutil
import logging
import time
from pathlib import Path
from unittest.mock import patch, MagicMock
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from core.logging_system import (
    LoggingSystem, LogConfig, SensitiveDataFilter, ContextManager, 
    PerformanceTracker, CustomFormatter, ColoredConsoleHandler,
    get_logging_system, get_logger, log_context, log_performance
)


class TestLogConfig(unittest.TestCase):
    """LogConfig数据类测试"""
    
    def test_default_config(self):
        """测试默认配置"""
        config = LogConfig()
        
        self.assertEqual(config.level, "INFO")
        self.assertIsNone(config.file_path)
        self.assertEqual(config.max_file_size, "50MB")
        self.assertEqual(config.backup_count, 5)
        self.assertEqual(config.format_type, "detailed")
        self.assertTrue(config.console_output)
        self.assertTrue(config.colored_output)
        self.assertTrue(config.structured_logging)
        self.assertIn('password', config.sensitive_fields)
        self.assertIn('user_id', config.context_fields)
    
    def test_custom_config(self):
        """测试自定义配置"""
        config = LogConfig(
            level="DEBUG",
            file_path="/test/path.log",
            format_type="json",
            console_output=False,
            sensitive_fields=['custom_secret']
        )
        
        self.assertEqual(config.level, "DEBUG")
        self.assertEqual(config.file_path, "/test/path.log")
        self.assertEqual(config.format_type, "json")
        self.assertFalse(config.console_output)
        self.assertEqual(config.sensitive_fields, ['custom_secret'])


class TestSensitiveDataFilter(unittest.TestCase):
    """敏感数据过滤器测试"""
    
    def setUp(self):
        """测试前置准备"""
        self.filter = SensitiveDataFilter(['password', 'api_key', 'secret'])
    
    def test_filter_dict(self):
        """测试字典过滤"""
        data = {
            'username': 'testuser',
            'password': 'secret123',
            'api_key': 'abc123',
            'normal_field': 'normal_value',
            'nested': {
                'secret': 'hidden',
                'public': 'visible'
            }
        }
        
        filtered = self.filter.filter_dict(data)
        
        self.assertEqual(filtered['username'], 'testuser')
        self.assertEqual(filtered['password'], '***FILTERED***')
        self.assertEqual(filtered['api_key'], '***FILTERED***')
        self.assertEqual(filtered['normal_field'], 'normal_value')
        self.assertEqual(filtered['nested']['secret'], '***FILTERED***')
        self.assertEqual(filtered['nested']['public'], 'visible')
    
    def test_filter_string(self):
        """测试字符串过滤"""
        text = 'api_key="secret123" and password: "mypass"'
        filtered = self.filter.filter_string(text)
        
        self.assertIn('***FILTERED***', filtered)
        self.assertNotIn('secret123', filtered)
        self.assertNotIn('mypass', filtered)
    
    def test_filter_non_dict(self):
        """测试非字典数据"""
        result = self.filter.filter_dict("not a dict")
        self.assertEqual(result, "not a dict")


class TestContextManager(unittest.TestCase):
    """上下文管理器测试"""
    
    def setUp(self):
        """测试前置准备"""
        self.context_mgr = ContextManager()
    
    def test_set_get_context(self):
        """测试设置和获取上下文"""
        self.context_mgr.set_context(user_id="123", session_id="abc")
        
        context = self.context_mgr.get_context()
        self.assertEqual(context['user_id'], "123")
        self.assertEqual(context['session_id'], "abc")
    
    def test_clear_context(self):
        """测试清除上下文"""
        self.context_mgr.set_context(user_id="123")
        self.context_mgr.clear_context()
        
        context = self.context_mgr.get_context()
        self.assertEqual(context, {})
    
    def test_context_manager(self):
        """测试上下文管理器"""
        self.context_mgr.set_context(original="value")
        
        with self.context_mgr.context(temp="temp_value"):
            context = self.context_mgr.get_context()
            self.assertEqual(context['temp'], "temp_value")
            self.assertEqual(context['original'], "value")
        
        # 上下文应该恢复
        context = self.context_mgr.get_context()
        self.assertEqual(context['original'], "value")
        self.assertNotIn('temp', context)


class TestPerformanceTracker(unittest.TestCase):
    """性能追踪器测试"""
    
    def setUp(self):
        """测试前置准备"""
        self.tracker = PerformanceTracker()
    
    def test_track_log(self):
        """测试日志追踪"""
        self.tracker.track_log("INFO", 0.1)
        self.tracker.track_log("ERROR", 0.2)
        self.tracker.track_log("INFO", 0.15)
        
        stats = self.tracker.get_stats()
        
        self.assertEqual(stats['log_counts']['INFO'], 2)
        self.assertEqual(stats['log_counts']['ERROR'], 1)
        self.assertAlmostEqual(stats['avg_response_times']['INFO'], 0.125, places=3)
        self.assertAlmostEqual(stats['avg_response_times']['ERROR'], 0.2, places=3)
    
    def test_track_error(self):
        """测试错误追踪"""
        self.tracker.track_error("ValueError")
        self.tracker.track_error("KeyError")
        self.tracker.track_error("ValueError")
        
        stats = self.tracker.get_stats()
        
        self.assertEqual(stats['error_counts']['ValueError'], 2)
        self.assertEqual(stats['error_counts']['KeyError'], 1)
    
    def test_stats_uptime(self):
        """测试运行时间统计"""
        time.sleep(0.01)  # 等待一小段时间
        stats = self.tracker.get_stats()
        
        self.assertGreater(stats['uptime'], 0)


class TestCustomFormatter(unittest.TestCase):
    """自定义格式化器测试"""
    
    def setUp(self):
        """测试前置准备"""
        self.config = LogConfig(format_type="detailed")
        self.filter = SensitiveDataFilter(['password', 'secret'])
        self.formatter = CustomFormatter(self.config, self.filter)
    
    def test_format_detailed(self):
        """测试详细格式"""
        record = logging.LogRecord(
            name="test",
            level=logging.INFO,
            pathname="test.py",
            lineno=10,
            msg="Test message",
            args=(),
            exc_info=None
        )
        
        formatted = self.formatter.format(record)
        
        self.assertIn("INFO", formatted)
        self.assertIn("Test message", formatted)
        self.assertIn("test", formatted)
    
    def test_format_json(self):
        """测试JSON格式"""
        config = LogConfig(format_type="json")
        formatter = CustomFormatter(config, self.filter)
        
        record = logging.LogRecord(
            name="test",
            level=logging.INFO,
            pathname="test.py",
            lineno=10,
            msg="Test message",
            args=(),
            exc_info=None
        )
        
        formatted = formatter.format(record)
        
        # 应该是有效的JSON
        data = json.loads(formatted)
        self.assertEqual(data['level'], 'INFO')
        self.assertEqual(data['message'], 'Test message')
        self.assertEqual(data['logger'], 'test')
    
    def test_sensitive_filtering(self):
        """测试敏感信息过滤"""
        record = logging.LogRecord(
            name="test",
            level=logging.INFO,
            pathname="test.py",
            lineno=10,
            msg="password=secret123",
            args=(),
            exc_info=None
        )
        
        formatted = self.formatter.format(record)
        
        self.assertIn("***FILTERED***", formatted)
        self.assertNotIn("secret123", formatted)


class TestLoggingSystem(unittest.TestCase):
    """日志系统主类测试"""
    
    def setUp(self):
        """测试前置准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.log_file = os.path.join(self.temp_dir, 'test.log')
        
        self.config = LogConfig(
            level="DEBUG",
            file_path=self.log_file,
            format_type="detailed",
            console_output=False,  # 关闭控制台输出以便测试
            structured_logging=False  # 简化测试
        )
        
        self.logging_system = LoggingSystem(self.config)
    
    def tearDown(self):
        """测试后清理"""
        self.logging_system.shutdown()
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_get_logger(self):
        """测试获取日志器"""
        logger = self.logging_system.get_logger('test')
        
        self.assertIsInstance(logger, logging.Logger)
        self.assertEqual(logger.name, 'test')
        self.assertEqual(logger.level, logging.DEBUG)
    
    def test_logger_caching(self):
        """测试日志器缓存"""
        logger1 = self.logging_system.get_logger('test')
        logger2 = self.logging_system.get_logger('test')
        
        # 应该返回同一个实例
        self.assertIs(logger1, logger2)
    
    def test_set_level(self):
        """测试动态设置日志级别"""
        logger = self.logging_system.get_logger('test')
        
        self.logging_system.set_level('ERROR')
        
        self.assertEqual(logger.level, logging.ERROR)
        self.assertEqual(self.config.level, 'ERROR')
    
    def test_context_management(self):
        """测试上下文管理"""
        self.logging_system.add_context(user_id="123")
        
        context = self.logging_system.context_manager.get_context()
        self.assertEqual(context['user_id'], "123")
        
        self.logging_system.clear_context()
        context = self.logging_system.context_manager.get_context()
        self.assertEqual(context, {})
    
    def test_context_manager(self):
        """测试上下文管理器"""
        with self.logging_system.context(temp_id="456"):
            context = self.logging_system.context_manager.get_context()
            self.assertEqual(context['temp_id'], "456")
        
        # 上下文应该清除
        context = self.logging_system.context_manager.get_context()
        self.assertEqual(context, {})
    
    def test_log_performance(self):
        """测试性能日志记录"""
        self.logging_system.log_performance("test_operation", 0.5, extra_info="test")
        
        # 检查统计
        stats = self.logging_system.get_stats()
        self.assertGreater(stats['log_counts'].get('INFO', 0), 0)
    
    def test_log_error(self):
        """测试错误日志记录"""
        try:
            raise ValueError("Test error")
        except ValueError as e:
            self.logging_system.log_error(e, {"context": "test"})
        
        # 检查错误统计
        stats = self.logging_system.get_stats()
        self.assertEqual(stats['error_counts']['ValueError'], 1)
    
    def test_file_logging(self):
        """测试文件日志记录"""
        logger = self.logging_system.get_logger('file_test')
        logger.info("测试文件日志")
        
        # 强制刷新
        for handler in logger.handlers:
            handler.flush()
        
        # 等待写入完成
        time.sleep(0.1)
        
        # 检查文件是否存在且包含内容
        self.assertTrue(os.path.exists(self.log_file))
        
        with open(self.log_file, 'r', encoding='utf-8') as f:
            content = f.read()
            self.assertIn("测试文件日志", content)
    
    def test_file_size_parsing(self):
        """测试文件大小解析"""
        test_cases = [
            ("50MB", 50 * 1024 * 1024),
            ("1GB", 1024 * 1024 * 1024),
            ("500KB", 500 * 1024),
            ("100B", 100),
            ("invalid", 50 * 1024 * 1024)  # 默认值
        ]
        
        for size_str, expected in test_cases:
            result = self.logging_system._parse_file_size(size_str)
            self.assertEqual(result, expected, f"解析 {size_str} 失败")


class TestGlobalFunctions(unittest.TestCase):
    """全局函数测试"""
    
    def setUp(self):
        """测试前置准备"""
        # 重置全局日志系统
        import core.logging_system
        core.logging_system._logging_system = None
    
    def test_get_logging_system(self):
        """测试获取全局日志系统"""
        config = LogConfig(level="DEBUG", console_output=False)
        
        system1 = get_logging_system(config)
        system2 = get_logging_system()  # 第二次调用不传config
        
        # 应该返回同一个实例
        self.assertIs(system1, system2)
        self.assertEqual(system1.config.level, "DEBUG")
    
    def test_get_logger_convenience(self):
        """测试便捷日志器获取"""
        logger = get_logger('convenience_test')
        
        self.assertIsInstance(logger, logging.Logger)
        self.assertEqual(logger.name, 'convenience_test')
    
    def test_log_context_manager(self):
        """测试日志上下文管理器"""
        with log_context(test_id="789"):
            logging_system = get_logging_system()
            context = logging_system.context_manager.get_context()
            self.assertEqual(context['test_id'], "789")
    
    @patch('time.time')
    def test_performance_decorator(self, mock_time):
        """测试性能监控装饰器"""
        # 模拟时间流逝
        mock_time.side_effect = [1000.0, 1000.5]  # 0.5秒执行时间
        
        @log_performance("test_operation")
        def test_function():
            return "success"
        
        result = test_function()
        
        self.assertEqual(result, "success")
        
        # 检查性能统计
        logging_system = get_logging_system()
        stats = logging_system.get_stats()
        self.assertGreater(stats['log_counts'].get('INFO', 0), 0)


class TestColoredConsoleHandler(unittest.TestCase):
    """彩色控制台处理器测试"""
    
    def test_colors_enabled(self):
        """测试颜色启用"""
        # 创建模拟的TTY流
        mock_stream = MagicMock()
        mock_stream.isatty.return_value = True
        
        handler = ColoredConsoleHandler(mock_stream, use_colors=True)
        self.assertTrue(handler.use_colors)
    
    def test_colors_disabled(self):
        """测试颜色禁用"""
        mock_stream = MagicMock()
        mock_stream.isatty.return_value = False
        
        handler = ColoredConsoleHandler(mock_stream, use_colors=True)
        self.assertFalse(handler.use_colors)


if __name__ == '__main__':
    # 配置测试日志
    logging.basicConfig(level=logging.WARNING)
    
    # 运行测试
    unittest.main(verbosity=2)