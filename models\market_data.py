"""
DeepSeek量化交易系统 - 市场数据模型

此模块定义了所有市场数据相关的Pydantic模型，
包括K线、Ticker、订单簿、成交记录等。

Author: DeepSeek Trading System
Date: 2025-07-31
"""

from datetime import datetime
from decimal import Decimal
from typing import List, Optional, Dict, Any
from enum import Enum
from pydantic import Field, validator

from .base import BaseModel, TimestampMixin, ValidationMixin


class TimeFrame(str, Enum):
    """时间周期枚举"""
    M1 = "1m"      # 1分钟
    M3 = "3m"      # 3分钟  
    M5 = "5m"      # 5分钟
    M15 = "15m"    # 15分钟
    M30 = "30m"    # 30分钟
    H1 = "1h"      # 1小时
    H2 = "2h"      # 2小时
    H4 = "4h"      # 4小时
    H6 = "6h"      # 6小时
    H8 = "8h"      # 8小时
    H12 = "12h"    # 12小时
    D1 = "1d"      # 1天
    W1 = "1w"      # 1周
    MON1 = "1M"    # 1月


class PriceLevel(BaseModel, ValidationMixin):
    """
    价格档位模型
    
    用于表示订单簿中的买卖盘价格和数量信息。
    """
    
    price: Decimal = Field(description="价格")
    size: Decimal = Field(description="数量")
    count: Optional[int] = Field(None, description="订单数量")
    
    @validator('price', 'size')
    def validate_positive_values(cls, v):
        """验证价格和数量为正数"""
        if v <= 0:
            raise ValueError(f"价格和数量必须为正数，当前值: {v}")
        return v
    
    @property
    def notional(self) -> Decimal:
        """名义金额（价格 × 数量）"""
        return self.price * self.size


class OrderBook(BaseModel, TimestampMixin):
    """
    订单簿模型
    
    表示某个交易对在特定时刻的买卖盘深度信息。
    """
    
    symbol: str = Field(description="交易对符号")
    bids: List[PriceLevel] = Field(description="买盘（按价格降序）")
    asks: List[PriceLevel] = Field(description="卖盘（按价格升序）")
    checksum: Optional[str] = Field(None, description="数据校验和")
    
    @validator('symbol')
    def validate_symbol_format(cls, v):
        """验证交易对格式"""
        if '-' not in v:
            raise ValueError(f"交易对格式应为 BASE-QUOTE，当前值: {v}")
        return v.upper()
    
    @validator('bids')
    def validate_bids_order(cls, v):
        """验证买盘按价格降序排列"""
        if len(v) > 1:
            for i in range(len(v) - 1):
                if v[i].price <= v[i + 1].price:
                    raise ValueError("买盘价格必须按降序排列")
        return v
    
    @validator('asks')
    def validate_asks_order(cls, v):
        """验证卖盘按价格升序排列"""
        if len(v) > 1:
            for i in range(len(v) - 1):
                if v[i].price >= v[i + 1].price:
                    raise ValueError("卖盘价格必须按升序排列")
        return v
    
    @property
    def best_bid(self) -> Optional[PriceLevel]:
        """最佳买价"""
        return self.bids[0] if self.bids else None
    
    @property
    def best_ask(self) -> Optional[PriceLevel]:
        """最佳卖价"""
        return self.asks[0] if self.asks else None
    
    @property
    def spread(self) -> Optional[Decimal]:
        """买卖价差"""
        if self.best_bid and self.best_ask:
            return self.best_ask.price - self.best_bid.price
        return None
    
    @property
    def mid_price(self) -> Optional[Decimal]:
        """中间价"""
        if self.best_bid and self.best_ask:
            return (self.best_bid.price + self.best_ask.price) / 2
        return None
    
    def get_depth(self, side: str, depth: int = 5) -> List[PriceLevel]:
        """
        获取指定深度的盘口数据
        
        Args:
            side: 'bid' 或 'ask'
            depth: 深度层数
            
        Returns:
            价格档位列表
        """
        if side.lower() == 'bid':
            return self.bids[:depth]
        elif side.lower() == 'ask':
            return self.asks[:depth]
        else:
            raise ValueError("side must be 'bid' or 'ask'")


class Ticker(BaseModel, TimestampMixin):
    """
    Ticker行情模型
    
    表示交易对的24小时统计数据。
    """
    
    symbol: str = Field(description="交易对符号")
    last_price: Decimal = Field(description="最新价格")
    best_bid_price: Optional[Decimal] = Field(None, description="最佳买价")
    best_ask_price: Optional[Decimal] = Field(None, description="最佳卖价")
    best_bid_size: Optional[Decimal] = Field(None, description="最佳买量")
    best_ask_size: Optional[Decimal] = Field(None, description="最佳卖量")
    
    # 24小时统计
    high_24h: Optional[Decimal] = Field(None, description="24小时最高价")
    low_24h: Optional[Decimal] = Field(None, description="24小时最低价")
    volume_24h: Optional[Decimal] = Field(None, description="24小时成交量")
    volume_24h_quote: Optional[Decimal] = Field(None, description="24小时成交额")
    price_change_24h: Optional[Decimal] = Field(None, description="24小时价格变化")
    price_change_percent_24h: Optional[Decimal] = Field(None, description="24小时价格变化百分比")
    
    # 额外统计信息
    open_price_24h: Optional[Decimal] = Field(None, description="24小时开盘价")
    count_24h: Optional[int] = Field(None, description="24小时成交笔数")
    
    @validator('symbol')
    def validate_symbol_format(cls, v):
        """验证交易对格式"""
        if '-' not in v:
            raise ValueError(f"交易对格式应为 BASE-QUOTE，当前值: {v}")
        return v.upper()
    
    @validator('last_price', 'best_bid_price', 'best_ask_price', 'high_24h', 'low_24h')
    def validate_positive_prices(cls, v):
        """验证价格为正数"""
        if v is not None and v <= 0:
            raise ValueError(f"价格必须为正数，当前值: {v}")
        return v
    
    @property
    def spread(self) -> Optional[Decimal]:
        """买卖价差"""
        if self.best_bid_price and self.best_ask_price:
            return self.best_ask_price - self.best_bid_price
        return None
    
    @property
    def mid_price(self) -> Optional[Decimal]:
        """中间价"""
        if self.best_bid_price and self.best_ask_price:
            return (self.best_bid_price + self.best_ask_price) / 2
        return None
    
    def is_price_up(self) -> bool:
        """价格是否上涨"""
        return self.price_change_24h is not None and self.price_change_24h > 0
    
    def is_price_down(self) -> bool:
        """价格是否下跌"""
        return self.price_change_24h is not None and self.price_change_24h < 0


class Kline(BaseModel, TimestampMixin):
    """
    K线数据模型
    
    表示某个时间周期内的OHLCV数据。
    """
    
    symbol: str = Field(description="交易对符号")
    timeframe: TimeFrame = Field(description="时间周期")
    open_price: Decimal = Field(description="开盘价")
    high_price: Decimal = Field(description="最高价")
    low_price: Decimal = Field(description="最低价")
    close_price: Decimal = Field(description="收盘价")
    volume: Decimal = Field(description="成交量")
    volume_quote: Optional[Decimal] = Field(None, description="成交额")
    count: Optional[int] = Field(None, description="成交笔数")
    
    # K线时间信息
    open_time: datetime = Field(description="K线开始时间")
    close_time: datetime = Field(description="K线结束时间")
    
    @validator('symbol')
    def validate_symbol_format(cls, v):
        """验证交易对格式"""
        if '-' not in v:
            raise ValueError(f"交易对格式应为 BASE-QUOTE，当前值: {v}")
        return v.upper()
    
    @validator('open_price', 'high_price', 'low_price', 'close_price')
    def validate_positive_prices(cls, v):
        """验证价格为正数"""
        if v <= 0:
            raise ValueError(f"价格必须为正数，当前值: {v}")
        return v
    
    @validator('volume')
    def validate_positive_volume(cls, v):
        """验证成交量为非负数"""
        if v < 0:
            raise ValueError(f"成交量不能为负数，当前值: {v}")
        return v
    
    @validator('close_time')
    def validate_time_order(cls, v, values):
        """验证时间顺序"""
        if 'open_time' in values and v <= values['open_time']:
            raise ValueError("结束时间必须大于开始时间")
        return v
    
    @validator('high_price')
    def validate_high_price(cls, v, values):
        """验证最高价"""
        if 'open_price' in values and v < values['open_price']:
            raise ValueError("最高价不能低于开盘价")
        if 'low_price' in values and v < values['low_price']:
            raise ValueError("最高价不能低于最低价")
        if 'close_price' in values and v < values['close_price']:
            raise ValueError("最高价不能低于收盘价")
        return v
    
    @validator('low_price')
    def validate_low_price(cls, v, values):
        """验证最低价"""
        if 'open_price' in values and v > values['open_price']:
            raise ValueError("最低价不能高于开盘价")
        if 'close_price' in values and v > values['close_price']:
            raise ValueError("最低价不能高于收盘价")
        return v
    
    @property
    def price_change(self) -> Decimal:
        """价格变化"""
        return self.close_price - self.open_price
    
    @property
    def price_change_percent(self) -> Decimal:
        """价格变化百分比"""
        if self.open_price == 0:
            return Decimal(0)
        return (self.price_change / self.open_price) * 100
    
    @property
    def amplitude(self) -> Decimal:
        """振幅"""
        if self.open_price == 0:
            return Decimal(0)
        return ((self.high_price - self.low_price) / self.open_price) * 100
    
    @property
    def body_size(self) -> Decimal:
        """实体大小"""
        return abs(self.close_price - self.open_price)
    
    @property
    def upper_shadow(self) -> Decimal:
        """上影线长度"""
        return self.high_price - max(self.open_price, self.close_price)
    
    @property
    def lower_shadow(self) -> Decimal:
        """下影线长度"""
        return min(self.open_price, self.close_price) - self.low_price
    
    def is_bullish(self) -> bool:
        """是否为阳线"""
        return self.close_price > self.open_price
    
    def is_bearish(self) -> bool:
        """是否为阴线"""
        return self.close_price < self.open_price
    
    def is_doji(self, threshold_percent: Decimal = Decimal('0.1')) -> bool:
        """
        是否为十字星
        
        Args:
            threshold_percent: 判断阈值（百分比）
            
        Returns:
            是否为十字星
        """
        if self.open_price == 0:
            return False
        
        body_percent = (self.body_size / self.open_price) * 100
        return body_percent <= threshold_percent


class Trade(BaseModel, TimestampMixin):
    """
    成交记录模型
    
    表示市场上的单笔成交记录。
    """
    
    symbol: str = Field(description="交易对符号")
    trade_id: str = Field(description="成交ID")
    price: Decimal = Field(description="成交价格")
    size: Decimal = Field(description="成交数量")
    side: str = Field(description="成交方向（buy/sell）")
    
    @validator('symbol')
    def validate_symbol_format(cls, v):
        """验证交易对格式"""
        if '-' not in v:
            raise ValueError(f"交易对格式应为 BASE-QUOTE，当前值: {v}")
        return v.upper()
    
    @validator('price', 'size')
    def validate_positive_values(cls, v):
        """验证价格和数量为正数"""
        if v <= 0:
            raise ValueError(f"价格和数量必须为正数，当前值: {v}")
        return v
    
    @validator('side')
    def validate_side(cls, v):
        """验证成交方向"""
        if v.lower() not in ['buy', 'sell']:
            raise ValueError(f"成交方向必须为 buy 或 sell，当前值: {v}")
        return v.lower()
    
    @property
    def notional(self) -> Decimal:
        """成交金额"""
        return self.price * self.size


class MarketDataSnapshot(BaseModel, TimestampMixin):
    """
    市场数据快照
    
    包含某个交易对在特定时刻的完整市场数据。
    """
    
    symbol: str = Field(description="交易对符号")
    ticker: Optional[Ticker] = Field(None, description="Ticker数据")
    orderbook: Optional[OrderBook] = Field(None, description="订单簿数据")
    recent_trades: List[Trade] = Field(default_factory=list, description="最近成交记录")
    
    @validator('symbol')
    def validate_symbol_format(cls, v):
        """验证交易对格式"""
        if '-' not in v:
            raise ValueError(f"交易对格式应为 BASE-QUOTE，当前值: {v}")
        return v.upper()
    
    def is_complete(self) -> bool:
        """是否包含完整的市场数据"""
        return self.ticker is not None and self.orderbook is not None
    
    def get_last_price(self) -> Optional[Decimal]:
        """获取最新价格"""
        if self.ticker:
            return self.ticker.last_price
        elif self.recent_trades:
            return self.recent_trades[-1].price
        return None


class MarketData(BaseModel, TimestampMixin):
    """
    综合市场数据模型
    
    用于存储和传递完整的市场数据信息。
    """
    
    symbol: str = Field(description="交易对符号")
    snapshots: Dict[str, MarketDataSnapshot] = Field(
        default_factory=dict,
        description="市场数据快照（按时间戳索引）"
    )
    klines: Dict[TimeFrame, List[Kline]] = Field(
        default_factory=dict,
        description="K线数据（按时间周期分组）"
    )
    metadata: Dict[str, Any] = Field(
        default_factory=dict,
        description="元数据信息"
    )
    
    @validator('symbol')
    def validate_symbol_format(cls, v):
        """验证交易对格式"""
        if '-' not in v:
            raise ValueError(f"交易对格式应为 BASE-QUOTE，当前值: {v}")
        return v.upper()
    
    def add_snapshot(self, snapshot: MarketDataSnapshot) -> None:
        """
        添加市场数据快照
        
        Args:
            snapshot: 市场数据快照
        """
        key = snapshot.timestamp.isoformat()
        self.snapshots[key] = snapshot
    
    def add_klines(self, timeframe: TimeFrame, klines: List[Kline]) -> None:
        """
        添加K线数据
        
        Args:
            timeframe: 时间周期
            klines: K线数据列表
        """
        if timeframe not in self.klines:
            self.klines[timeframe] = []
        
        self.klines[timeframe].extend(klines)
        # 按时间排序
        self.klines[timeframe].sort(key=lambda k: k.open_time)
    
    def get_latest_snapshot(self) -> Optional[MarketDataSnapshot]:
        """获取最新的市场数据快照"""
        if not self.snapshots:
            return None
        
        latest_key = max(self.snapshots.keys())
        return self.snapshots[latest_key]
    
    def get_latest_kline(self, timeframe: TimeFrame) -> Optional[Kline]:
        """
        获取指定时间周期的最新K线
        
        Args:
            timeframe: 时间周期
            
        Returns:
            最新K线或None
        """
        if timeframe not in self.klines or not self.klines[timeframe]:
            return None
        
        return self.klines[timeframe][-1]
    
    def get_klines_range(self, timeframe: TimeFrame, start_time: datetime, end_time: datetime) -> List[Kline]:
        """
        获取指定时间范围的K线数据
        
        Args:
            timeframe: 时间周期
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            K线数据列表
        """
        if timeframe not in self.klines:
            return []
        
        return [
            kline for kline in self.klines[timeframe]
            if start_time <= kline.open_time <= end_time
        ]