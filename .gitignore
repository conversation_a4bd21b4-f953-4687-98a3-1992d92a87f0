# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# 虚拟环境
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# 配置文件(包含敏感信息)
config_production.json
config_development.json
config.json
.env

# 密钥文件
keys/
*.key
*.pem

# 数据库文件
*.db
*.sqlite
*.sqlite3
data/trading_system.db

# 日志文件
logs/
*.log

# 缓存目录
cache/
data/cache/
.cache/

# 备份文件
backups/
*.bak

# 数据导出
exports/
data/exports/

# 临时文件
temp/
tmp/
*.tmp

# 系统文件
.DS_Store
Thumbs.db
ehthumbs.db
Desktop.ini

# 测试覆盖率
.coverage
htmlcov/
.pytest_cache/

# 性能分析
.prof

# 监控数据
monitoring_data/

# wheel文件
*.whl