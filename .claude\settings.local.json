{"permissions": {"allow": ["<PERSON><PERSON>(dir:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "<PERSON><PERSON>(mkdir:*)", "Bash(python -m pytest tests/unit/test_config_manager.py -v)", "Bash(python -m pytest tests/unit/test_config_manager.py::TestConfigManager::test_init_with_existing_config -v)", "Bash(python -m pytest tests/unit/test_config_manager.py::TestConfigManager::test_get_nested_key tests/unit/test_config_manager.py::TestConfigManager::test_set_nested_key tests/unit/test_config_manager.py::TestConfigManager::test_validation_success -v)", "<PERSON><PERSON>(python:*)", "Bash(pip install:*)"], "deny": []}}